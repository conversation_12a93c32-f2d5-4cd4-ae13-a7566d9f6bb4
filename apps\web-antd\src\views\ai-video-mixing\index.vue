<script lang="ts" setup>
import type { VideoListItem } from '#/api/core/digital-human';

import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Carousel,
  Checkbox,
  Drawer,
  Empty,
  Input,
  InputNumber,
  message,
  Modal,
  Pagination,
  Radio,
  Slider,
  Switch,
  Upload,
} from 'ant-design-vue';

import {
  getVideoList,
  initOSSConfig,
  isOSSReady,
  uploadDigitalHumanVideo,
} from '#/api/core/digital-human';
import {
  createClipTask,
  getMusicCategories,
  getMusicList,
  getVideoBackgroundList,
  getVideoTemplateList,
} from '#/api/core/video-mixing';

defineOptions({ name: 'AIVideoMixing' });

// 初始化路由
const router = useRouter();

// 弹窗显示状态
const drawerVisible = ref(false);

// 视频列表数据
const videoList = ref<VideoListItem[]>([]);
const videoLoading = ref(false);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 12,
  total: 0,
  totalPage: 0,
});

// 视频对象接口定义
interface SelectedVideoItem {
  id: number;
  result_cover: string;
  isOpen: boolean;
  disabled: boolean;
  setContent: any[];
  content: any[];
  finalContent: any[];
  keynote_content: any[];
}

// 音频对象接口定义
interface SelectedMusicItem {
  id: string;
  play_url: string;
  volume: number;
}

// 选中的视频列表（最终确认的）
const selectedVideos = ref<SelectedVideoItem[]>([]);
// 临时选中的视频列表（抽屉中的选择状态）
const tempSelectedVideos = ref<number[]>([]);

// 控制项状态
const isCaptions = ref(false); // 字幕识别开关
const titleDisplayMode = ref(3); // 视频标题显示方式：3-开场3秒显示，0-全程显示
const videoTitles = ref(['']); // 视频标题列表

// 智能剪辑控制
const captions_switch = ref(true); // 智能剪辑总开关
const batchClipMultiple = ref(1); // 批量剪辑倍数
const uploadedVideos = ref<any[]>([]); // 上传的视频文件列表

// OSS上传相关状态
const ossConfigLoading = ref(false); // OSS配置加载状态
const uploadingFiles = ref<Map<string, number>>(new Map()); // 正在上传的文件及其进度

// 视频模版相关状态
const templateList = ref<any[]>([]); // 模版列表
const templateLoading = ref(false); // 模版加载状态
const selectedTemplates = ref<{ id: number; url: string }[]>([]); // 选中的模版

// 视频背景相关状态
const backgroundList = ref<any[]>([]); // 背景列表
const backgroundLoading = ref(false); // 背景加载状态
const selectedBackgroundId = ref<number | string>(''); // 选中的背景ID，默认选中第一个

// 背景音乐相关状态
const musicCategories = ref<any[]>([]); // 音乐分类列表
const musicCategoriesLoading = ref(false); // 音乐分类加载状态
const musicList = ref<any[]>([]); // 音乐列表
const musicListLoading = ref(false); // 音乐列表加载状态
const selectedMusicCategory = ref<string>(''); // 选中的音乐分类ID
const selectedMusics = ref<SelectedMusicItem[]>([]); // 选中的音乐列表
const currentPlayingMusic = ref<string>(''); // 当前播放的音乐ID
const audioPlayer = ref<HTMLAudioElement | null>(null); // 音频播放器

// 生成视频相关状态
const isSubmitting = ref(false); // 防重复提交

// 音量调节相关状态
const volumeModalVisible = ref(false); // 音量调节弹窗显示状态
const currentEditingMusic = ref<null | SelectedMusicItem>(null); // 当前正在编辑音量的音乐
const tempVolume = ref(10); // 临时音量值

// 打开视频选择弹窗
const openVideoDrawer = () => {
  drawerVisible.value = true;
  // 初始化临时选中状态为当前已确认的选中状态（提取ID）
  tempSelectedVideos.value = selectedVideos.value.map((video) => video.id);
  loadVideoList();
};

// 关闭弹窗
const closeDrawer = () => {
  drawerVisible.value = false;
  // 清空临时选中状态
  tempSelectedVideos.value = [];
};

// 加载视频列表（使用真实API）
const loadVideoList = async (page: number = 1) => {
  try {
    videoLoading.value = true;
    const params = {
      current_status: 'success',
      page,
      psize: pagination.value.pageSize,
    };

    const response = await getVideoList(params as any);
    videoList.value = response.list;

    // 将新加载的视频添加到全部视频列表中（避免重复）
    response.list.forEach((video) => {
      if (!allVideos.value.some((v) => v.id === video.id)) {
        allVideos.value.push(video);
      }
    });

    pagination.value = {
      current: response.pindex,
      pageSize: response.psize,
      total: response.total,
      totalPage: response.totalPage,
    };
  } catch (error) {
    console.error('获取视频列表失败:', error);
    message.error('获取视频列表失败，请重试');
    videoList.value = [];
    pagination.value.total = 0;
  } finally {
    videoLoading.value = false;
  }
};

// 跳转到AI数字人页面
const goToDigitalHuman = () => {
  router.push('/public-domain/ai-digital-human');
};

// 切换视频选择状态（临时状态）
const toggleVideoSelection = (videoId: number) => {
  const index = tempSelectedVideos.value.indexOf(videoId);
  if (index === -1) {
    tempSelectedVideos.value.push(videoId);
  } else {
    tempSelectedVideos.value.splice(index, 1);
  }
};

// 分页切换
const handlePageChange = (page: number) => {
  loadVideoList(page);
};

// 确认选择
const confirmSelection = () => {
  // 将临时选中状态转换为完整的视频对象
  selectedVideos.value = tempSelectedVideos.value.map((videoId) => {
    const video = allVideos.value.find((v) => v.id === videoId);
    return {
      id: videoId,
      result_cover: video?.result_cover || '',
      isOpen: false,
      disabled: true,
      setContent: [],
      content: [],
      finalContent: [],
      keynote_content: [],
    };
  });
  message.success(`已选择 ${selectedVideos.value.length} 个视频`);
  closeDrawer();
};

// 存储所有已加载的视频信息
const allVideos = ref<VideoListItem[]>([]);

// 根据ID获取选中的视频信息
const getSelectedVideoById = (videoId: number) => {
  return (
    selectedVideos.value.find((video) => video.id === videoId) ||
    allVideos.value.find((video) => video.id === videoId)
  );
};

// 移除选中的视频
const removeSelectedVideo = (videoId: number) => {
  const index = selectedVideos.value.findIndex((video) => video.id === videoId);
  if (index !== -1) {
    selectedVideos.value.splice(index, 1);
  }
};

// 视频标题管理
const addTitleInput = () => {
  videoTitles.value.push('');
};

const removeTitleInput = (index: number) => {
  if (videoTitles.value.length > 1) {
    videoTitles.value.splice(index, 1);
  }
};

// 获取视频时长
const getVideoDuration = (file: File): Promise<number> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.addEventListener('loadedmetadata', () => {
      window.URL.revokeObjectURL(video.src);
      resolve(video.duration);
    });

    video.addEventListener('error', () => {
      resolve(0);
    });

    video.src = URL.createObjectURL(file);
  });
};

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (seconds === 0) return '未知';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return minutes > 0
    ? `${minutes}分${remainingSeconds}秒`
    : `${remainingSeconds}秒`;
};

// 视频上传管理
const beforeUpload = async (file: any) => {
  // 检查OSS配置是否就绪
  if (!isOSSReady()) {
    message.error('上传配置未就绪，请稍后重试');
    return false;
  }

  // 检查文件类型
  const isVideo = file.type.startsWith('video/');
  if (!isVideo) {
    message.error('只能上传视频文件！');
    return false;
  }

  // 检查文件大小（100MB = 100 * 1024 * 1024 bytes）
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error('视频文件大小不能超过100MB!');
    return false;
  }

  // 检查上传数量限制
  if (uploadedVideos.value.length >= 10) {
    message.error('最多只能上传10个视频!');
    return false;
  }

  // 获取视频时长
  const duration = await getVideoDuration(file);

  // 检查视频时长（不能低于10秒）
  if (duration < 10) {
    message.error('视频时长不能低于10秒!');
    return false;
  }

  // 开始上传到OSS
  try {
    const fileId = Date.now().toString();
    uploadingFiles.value.set(fileId, 0);

    console.warn('开始上传视频到OSS:', file.name);

    // 使用OSS上传器上传视频
    const result = await uploadDigitalHumanVideo(file, (progress) => {
      uploadingFiles.value.set(fileId, progress);
      console.warn(`上传进度: ${progress}%`);
    });

    // 上传成功，移除上传状态
    uploadingFiles.value.delete(fileId);

    // 添加到上传列表
    uploadedVideos.value.push({
      uid: fileId,
      name: file.name,
      status: 'done',
      url: result.url, // 使用真实的OSS URL
      duration,
      size: file.size,
      ossResult: result, // 保存完整的OSS上传结果
    });

    console.warn('视频上传成功，OSS URL:', result.url);
    message.success(`视频上传成功，时长：${formatDuration(duration)}`);
  } catch (error) {
    console.error('视频上传失败:', error);
    const errorMessage = error instanceof Error ? error.message : '请重试';
    message.error(`视频上传失败: ${errorMessage}`);

    // 清理上传状态
    const fileId = Date.now().toString();
    uploadingFiles.value.delete(fileId);
  }

  return false; // 阻止Ant Design的默认上传行为
};

const removeUploadedVideo = (file: any) => {
  const index = uploadedVideos.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    uploadedVideos.value.splice(index, 1);
  }
};

// 播放视频
const playVideo = (file: any) => {
  // 创建一个临时的video元素来播放视频
  const video = document.createElement('video');
  video.src = file.url;
  video.controls = true;
  video.style.width = '100%';
  video.style.height = 'auto';
  video.style.maxWidth = '600px';
  video.style.maxHeight = '400px';

  // 使用Modal显示视频播放器
  const modal = document.createElement('div');
  modal.style.position = 'fixed';
  modal.style.top = '0';
  modal.style.left = '0';
  modal.style.width = '100%';
  modal.style.height = '100%';
  modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  modal.style.display = 'flex';
  modal.style.alignItems = 'center';
  modal.style.justifyContent = 'center';
  modal.style.zIndex = '9999';

  const container = document.createElement('div');
  container.style.maxWidth = '600px';
  container.style.maxHeight = '400px';
  container.style.position = 'relative';
  container.style.background = 'black';
  container.style.borderRadius = '8px';
  container.style.overflow = 'hidden';

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '×';
  closeBtn.style.position = 'absolute';
  closeBtn.style.top = '-40px';
  closeBtn.style.right = '0';
  closeBtn.style.background = 'white';
  closeBtn.style.border = 'none';
  closeBtn.style.borderRadius = '50%';
  closeBtn.style.width = '30px';
  closeBtn.style.height = '30px';
  closeBtn.style.cursor = 'pointer';
  closeBtn.style.fontSize = '20px';
  closeBtn.style.color = '#333';
  closeBtn.style.display = 'flex';
  closeBtn.style.alignItems = 'center';
  closeBtn.style.justifyContent = 'center';

  closeBtn.addEventListener('click', () => {
    modal.remove();
  });

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });

  container.append(video);
  container.append(closeBtn);
  modal.append(container);
  document.body.append(modal);

  video.play();
};

// 初始化OSS配置
const initializeOSSConfig = async () => {
  try {
    ossConfigLoading.value = true;
    await initOSSConfig();
    console.warn('阿里云OSS配置初始化成功');
  } catch (error) {
    console.error('获取阿里云OSS配置失败:', error);
    message.error('获取上传配置失败，请重试');
  } finally {
    ossConfigLoading.value = false;
  }
};

// 获取视频模版列表
const loadTemplateList = async () => {
  try {
    templateLoading.value = true;
    const response = await getVideoTemplateList();
    console.warn('模版列表API响应:', response);

    // 根据实际返回的数据结构进行处理
    templateList.value = response.list || response || [];
    console.warn('模版列表加载成功:', templateList.value);
  } catch (error) {
    console.error('获取模版列表失败:', error);
    message.error('获取模版列表失败，请重试');
    templateList.value = [];
  } finally {
    templateLoading.value = false;
  }
};

// 切换模版选择状态
const toggleTemplateSelection = (template: any) => {
  const existingIndex = selectedTemplates.value.findIndex(
    (item) => item.id === template.id,
  );

  if (existingIndex === -1) {
    // 未选中，添加选中
    selectedTemplates.value.push({
      id: template.id,
      url: template.url,
    });
  } else {
    // 已选中，取消选中
    selectedTemplates.value.splice(existingIndex, 1);
  }
};

// 检查模版是否已选中
const isTemplateSelected = (templateId: number) => {
  return selectedTemplates.value.some((item) => item.id === templateId);
};

// 获取视频背景列表
const loadBackgroundList = async () => {
  try {
    backgroundLoading.value = true;
    const response = await getVideoBackgroundList();
    console.warn('背景列表API响应:', response);

    // 在列表第一项增加默认背景
    const defaultBackground = {
      id: '',
      oss_url: 'https://szr.jiajs.cn/index/371.png',
      url: 'https://szr.jiajs.cn/index/371.png',
      create_time: null,
      delete_time: null,
      update_time: null,
    };

    // 根据实际返回的数据结构进行处理
    const apiData = response.list || response || [];
    backgroundList.value = [defaultBackground, ...apiData];
    console.warn('背景列表加载成功:', backgroundList.value);
  } catch (error) {
    console.error('获取背景列表失败:', error);
    message.error('获取背景列表失败，请重试');
    // 即使API失败，也要保证有默认背景
    backgroundList.value = [
      {
        id: '',
        oss_url: 'https://szr.jiajs.cn/index/371.png',
        url: 'https://szr.jiajs.cn/index/371.png',
        create_time: null,
        delete_time: null,
        update_time: null,
      },
    ];
  } finally {
    backgroundLoading.value = false;
  }
};

// 选择背景
const selectBackground = (backgroundId: number | string) => {
  selectedBackgroundId.value = backgroundId;
};

// 检查背景是否已选中
const isBackgroundSelected = (backgroundId: number | string) => {
  return selectedBackgroundId.value === backgroundId;
};

// 格式化音乐时长（秒转分钟）
const formatMusicDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 获取音乐分类列表
const loadMusicCategories = async () => {
  try {
    musicCategoriesLoading.value = true;
    const response = await getMusicCategories();
    console.warn('音乐分类API响应:', response);

    // 根据实际返回的数据结构进行处理
    musicCategories.value = response.data || response || [];

    // 默认选择第一个分类并加载其音乐列表
    if (musicCategories.value.length > 0) {
      selectedMusicCategory.value = musicCategories.value[0].id_str;
      loadMusicList(musicCategories.value[0].id_str);
    }

    console.warn('音乐分类加载成功:', musicCategories.value);
  } catch (error) {
    console.error('获取音乐分类失败:', error);
    message.error('获取音乐分类失败，请重试');
    musicCategories.value = [];
  } finally {
    musicCategoriesLoading.value = false;
  }
};

// 获取音乐列表
const loadMusicList = async (categoryId: string) => {
  try {
    musicListLoading.value = true;
    const response = await getMusicList(categoryId);
    console.warn('音乐列表API响应:', response);

    // 根据实际返回的数据结构进行处理
    musicList.value = response.data || response || [];
    console.warn('音乐列表加载成功:', musicList.value);
  } catch (error) {
    console.error('获取音乐列表失败:', error);
    message.error('获取音乐列表失败，请重试');
    musicList.value = [];
  } finally {
    musicListLoading.value = false;
  }
};

// 选择音乐分类
const selectMusicCategory = (categoryId: string) => {
  selectedMusicCategory.value = categoryId;
  loadMusicList(categoryId);
};

// 切换音乐选择状态（多选）
const toggleMusicSelection = (music: any) => {
  const existingIndex = selectedMusics.value.findIndex(
    (item) => item.id === music.id_str,
  );

  if (existingIndex === -1) {
    // 未选中，添加选中，默认音量为10
    selectedMusics.value.push({
      id: music.id_str,
      play_url: music.play_url?.url_list?.[0] || '',
      volume: 10,
    });
  } else {
    // 已选中，取消选中
    selectedMusics.value.splice(existingIndex, 1);
  }
};

// 播放/暂停音乐
const toggleMusicPlay = (music: any) => {
  const playUrl = music.play_url?.url_list?.[0];
  if (!playUrl) {
    message.error('音频链接无效');
    return;
  }

  // 如果当前正在播放这首音乐，则暂停
  if (currentPlayingMusic.value === music.id_str) {
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      currentPlayingMusic.value = '';
    }
    return;
  }

  // 停止当前播放的音乐
  if (audioPlayer.value) {
    audioPlayer.value.pause();
  }

  // 创建新的音频播放器
  audioPlayer.value = new Audio(playUrl);
  currentPlayingMusic.value = music.id_str;

  // 播放结束后重置状态
  audioPlayer.value.addEventListener('ended', () => {
    currentPlayingMusic.value = '';
    audioPlayer.value = null;
  });

  // 播放出错时重置状态
  audioPlayer.value.addEventListener('error', () => {
    message.error('音频播放失败');
    currentPlayingMusic.value = '';
    audioPlayer.value = null;
  });

  // 开始播放
  audioPlayer.value.play().catch(() => {
    message.error('音频播放失败');
    currentPlayingMusic.value = '';
    audioPlayer.value = null;
  });
};

// 检查音乐分类是否已选中
const isMusicCategorySelected = (categoryId: string) => {
  return selectedMusicCategory.value === categoryId;
};

// 检查音乐是否已选中
const isMusicSelected = (musicId: string) => {
  return selectedMusics.value.some((item) => item.id === musicId);
};

// 检查音乐是否正在播放
const isMusicPlaying = (musicId: string) => {
  return currentPlayingMusic.value === musicId;
};

// 根据音乐ID获取音乐名称
const getMusicNameById = (musicId: string) => {
  const music = musicList.value.find((item) => item.id_str === musicId);
  return music?.title || '未知音乐';
};

// 打开音量调节弹窗
const openVolumeModal = (music: SelectedMusicItem) => {
  currentEditingMusic.value = music;
  tempVolume.value = music.volume;
  volumeModalVisible.value = true;
};

// 确认音量修改
const confirmVolumeChange = () => {
  if (currentEditingMusic.value) {
    const index = selectedMusics.value.findIndex(
      (item) => item.id === currentEditingMusic.value!.id,
    );
    if (index !== -1) {
      selectedMusics.value[index].volume = tempVolume.value;
    }
  }
  volumeModalVisible.value = false;
  currentEditingMusic.value = null;
};

// 取消音量修改
const cancelVolumeChange = () => {
  volumeModalVisible.value = false;
  currentEditingMusic.value = null;
  tempVolume.value = 10;
};

// 移除选中的音乐
const removeSelectedMusic = (musicId: string) => {
  const index = selectedMusics.value.findIndex((item) => item.id === musicId);
  if (index !== -1) {
    selectedMusics.value.splice(index, 1);
  }
};

// 页面初始化
onMounted(() => {
  // 初始化OSS配置
  initializeOSSConfig();
  // 加载模版列表
  loadTemplateList();
  // 加载背景列表
  loadBackgroundList();
  // 加载音乐分类
  loadMusicCategories();
});

// 生成视频任务
const saveClipTask = async () => {
  // 参数验证
  if (selectedVideos.value.length === 0) {
    message.error('请选择需要剪辑的作品');
    return;
  }

  // 检查标题是否完整填写
  for (let i = 0; i < videoTitles.value.length; i++) {
    if (!videoTitles.value[i]) {
      message.error('请完善标题');
      return;
    }
  }

  // 检查是否选择了剪辑模板
  if (selectedTemplates.value.length === 0) {
    message.error('请选择剪辑模板');
    return;
  }

  // 智能剪辑特殊验证：需要至少4个视频
  if (captions_switch.value && uploadedVideos.value.length < 4) {
    message.error('智能剪辑最低上传4个视频');
    return;
  }

  // 防重复提交
  if (isSubmitting.value) {
    return;
  }
  isSubmitting.value = true;

  try {
    const requestData = {
      title: videoTitles.value.join(','), // 主标题 多个
      video_id: selectedVideos.value, // 作品对象数组
      model_id: selectedTemplates.value.map((item) => item.id).join(','), // 模板ID 多个
      multiple: captions_switch.value ? batchClipMultiple.value : '', // 批量剪辑倍数
      background_music: selectedMusics.value.map((item) => ({
        volume: item.volume,
        url: item.play_url,
      })), // 背景音乐对象数组
      material_url: captions_switch.value
        ? uploadedVideos.value.map((item) => item.url).join(',')
        : '', // 素材URL
      background_img_id: selectedBackgroundId.value, // 背景图片ID
      title_show_time: titleDisplayMode.value, // 标题显示时间
    };

    console.warn('提交剪辑任务参数:', requestData);

    const result = await createClipTask(requestData);
    console.warn('剪辑任务API响应:', result);

    // 如果接口已经做了拦截，成功的请求会直接返回数据，失败的会抛出异常
    // 所以能执行到这里说明请求成功了
    message.success(result.message || '剪辑任务创建成功');
    // 3秒后恢复提交状态
    setTimeout(() => {
      isSubmitting.value = false;
      // 这里可以添加刷新任务列表的逻辑
      // getTaskList();
    }, 3000);
  } catch (error) {
    isSubmitting.value = false;
    console.error('创建剪辑任务失败:', error);
    message.error('创建剪辑任务失败，请重试');
  }
};

// 页面卸载时清理音频播放器
onUnmounted(() => {
  if (audioPlayer.value) {
    audioPlayer.value.pause();
    audioPlayer.value = null;
  }
  currentPlayingMusic.value = '';
});
</script>

<template>
  <Page auto-content-height>
    <div class="ai-video-mixing-container">
      <!-- 左侧整体区域 -->
      <div class="left-panel">
        <!-- 左列：视频选择区域 -->
        <div class="video-selection-column">
          <!-- 视频选择区域 -->
          <div class="video-selection-area">
            <!-- 未选择视频时显示选择框 -->
            <div
              v-if="selectedVideos.length === 0"
              class="video-selector"
              @click="openVideoDrawer"
            >
              <div class="selector-content">
                <div class="selector-text">点击选择视频</div>
              </div>
            </div>

            <!-- 已选择视频时显示轮播图 -->
            <div v-else class="selected-videos-display">
              <div class="video-carousel-container">
                <Carousel
                  :dots="selectedVideos.length > 1"
                  :autoplay="false"
                  :arrows="selectedVideos.length > 1"
                  :draggable="true"
                  class="video-carousel"
                >
                  <div
                    v-for="video in selectedVideos"
                    :key="video.id"
                    class="carousel-slide"
                  >
                    <div class="selected-video-item">
                      <img
                        :src="
                          video.result_cover ||
                          'https://via.placeholder.com/240x427/f0f0f0/cccccc?text=视频'
                        "
                        :alt="getSelectedVideoById(video.id)?.name || '视频'"
                        class="selected-video-cover"
                      />
                      <div
                        class="delete-button"
                        @click="removeSelectedVideo(video.id)"
                      >
                        <svg
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="delete-icon"
                        >
                          <path
                            d="M18 6L6 18M6 6l12 12"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Carousel>
              </div>

              <!-- 重新选择按钮 -->
              <div class="reselect-button-container">
                <Button
                  type="primary"
                  @click="openVideoDrawer"
                  class="reselect-button"
                >
                  重新选择
                </Button>
              </div>
            </div>
          </div>

          <!-- 左侧控制项区域 -->
          <div class="control-items-area">
            <!-- 字幕识别开关 -->
            <div class="control-item">
              <div class="switch-item">
                <span class="control-label">是否识别视频字幕</span>
                <Switch v-model:checked="isCaptions" />
              </div>
            </div>

            <!-- 视频标题显示方式 -->
            <div class="control-item">
              <div class="control-label-block">视频标题显示</div>
              <Radio.Group v-model:value="titleDisplayMode">
                <Radio :value="3">开场3秒显示</Radio>
                <Radio :value="0">全程显示标题</Radio>
              </Radio.Group>
            </div>

            <!-- 视频标题输入区域 -->
            <div class="control-item">
              <div class="control-label-block">视频标题</div>
              <div class="title-inputs">
                <div
                  v-for="(_, index) in videoTitles"
                  :key="index"
                  class="title-input-item"
                >
                  <Input
                    v-model:value="videoTitles[index]"
                    :maxlength="18"
                    placeholder="请输入视频标题"
                    class="title-input"
                  />
                  <Button
                    v-if="videoTitles.length > 1"
                    type="text"
                    danger
                    @click="removeTitleInput(index)"
                    class="remove-btn"
                  >
                    删除
                  </Button>
                </div>
                <Button
                  type="dashed"
                  @click="addTitleInput"
                  class="add-title-btn"
                >
                  + 添加标题
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右列：智能剪辑控制面板 -->
        <div class="smart-clip-panel">
          <!-- 视频模版选择区域 -->
          <div class="template-section">
            <div class="template-title">视频模版</div>
            <div class="template-container" v-loading="templateLoading">
              <div
                v-for="template in templateList"
                :key="template.id"
                class="template-item"
                :class="{ selected: isTemplateSelected(template.id) }"
                @click="toggleTemplateSelection(template)"
              >
                <img
                  :src="template.url"
                  :alt="`模版${template.id}`"
                  class="template-image"
                />
                <div
                  v-if="isTemplateSelected(template.id)"
                  class="template-selected-indicator"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="check-icon"
                  >
                    <path
                      d="M20 6L9 17l-5-5"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- 视频背景选择区域 -->
          <div class="background-section">
            <div class="background-title">视频背景</div>
            <div class="background-container" v-loading="backgroundLoading">
              <div
                v-for="background in backgroundList"
                :key="background.id"
                class="background-item"
                :class="{ selected: isBackgroundSelected(background.id) }"
                @click="selectBackground(background.id)"
              >
                <img
                  :src="background.url"
                  :alt="`背景${background.id || '默认'}`"
                  class="background-image"
                />
                <div
                  v-if="isBackgroundSelected(background.id)"
                  class="background-selected-indicator"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="check-icon"
                  >
                    <path
                      d="M20 6L9 17l-5-5"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- 背景音乐选择区域 -->
          <div class="music-section">
            <div class="music-title">背景音乐</div>

            <!-- 音乐分类 -->
            <div class="music-categories" v-loading="musicCategoriesLoading">
              <div
                v-for="category in musicCategories"
                :key="category.id_str"
                class="music-category-item"
                :class="{ selected: isMusicCategorySelected(category.id_str) }"
                @click="selectMusicCategory(category.id_str)"
              >
                {{ category.name }}
              </div>
            </div>

            <!-- 音乐列表 -->
            <div class="music-list-container" v-loading="musicListLoading">
              <div class="music-grid">
                <div
                  v-for="music in musicList"
                  :key="music.id_str"
                  class="music-item"
                  :class="{ selected: isMusicSelected(music.id_str) }"
                  @click="toggleMusicSelection(music)"
                >
                  <div class="music-content">
                    <!-- 播放按钮 -->
                    <img
                      :src="
                        isMusicPlaying(music.id_str)
                          ? 'https://szr.jiajs.cn/index/302.png'
                          : 'https://szr.jiajs.cn/index/301.png'
                      "
                      alt="播放"
                      class="play-button"
                      @click.stop="toggleMusicPlay(music)"
                    />

                    <div class="music-info">
                      <div class="music-title-text">{{ music.title }}</div>
                      <div class="music-meta">
                        <span class="music-author">{{ music.author }}</span>
                        <span class="music-duration">{{
                          formatMusicDuration(music.duration)
                        }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 选中指示器 -->
                  <div
                    v-if="isMusicSelected(music.id_str)"
                    class="music-selected-indicator"
                  >
                    <svg
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      class="check-icon"
                    >
                      <path
                        d="M20 6L9 17l-5-5"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已选择音频显示区域 -->
            <div
              v-if="selectedMusics.length > 0"
              class="selected-music-section"
            >
              <div class="selected-music-title">
                已选择音频 ({{ selectedMusics.length }})
              </div>
              <div class="selected-music-list">
                <div
                  v-for="music in selectedMusics"
                  :key="music.id"
                  class="selected-music-item"
                >
                  <div class="selected-music-info">
                    <div class="selected-music-name">
                      {{ getMusicNameById(music.id) }}
                    </div>
                    <div class="selected-music-volume">
                      音量: {{ music.volume }}
                    </div>
                  </div>
                  <div class="selected-music-actions">
                    <!-- 音量调节按钮 -->
                    <img
                      src="https://szr.jiajs.cn/index/437.png"
                      alt="音量调节"
                      class="volume-adjust-button"
                      @click="openVolumeModal(music)"
                    />
                    <!-- 删除按钮 -->
                    <div
                      class="remove-music-button"
                      @click="removeSelectedMusic(music.id)"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="remove-icon"
                      >
                        <path
                          d="M18 6L6 18M6 6l12 12"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 智能剪辑总开关和批量剪辑倍数 -->
          <div class="panel-item">
            <div class="switch-row">
              <div class="switch-item">
                <span class="panel-label">是否开启智能剪辑</span>
                <Switch v-model:checked="captions_switch" />
              </div>
              <div v-if="captions_switch" class="switch-item">
                <span class="panel-label">批量剪辑倍数</span>
                <InputNumber
                  v-model:value="batchClipMultiple"
                  :min="1"
                  :max="10"
                  class="batch-multiple-input"
                />
              </div>
            </div>
          </div>

          <!-- 智能剪辑开启时显示的内容 -->
          <div v-if="captions_switch" class="smart-clip-content">
            <!-- 多视频上传区域 -->
            <div class="panel-item">
              <div class="upload-title">
                上传多个视频（{{ uploadedVideos.length }}/10）
              </div>

              <!-- 说明文字 -->
              <div class="upload-description">
                请上传多个和你业务、产品、店铺、企业宣传相关的视频，用于批量去重剪辑，
                <span class="highlight-text"
                  >最低上传4个视频，视频时长不能低于10秒，视频小于100M</span
                >， 上传的视频越多剪辑去重效果越好
              </div>

              <!-- 上传区域 -->
              <div class="upload-area">
                <div class="video-upload-container">
                  <!-- 已上传视频列表 -->
                  <div
                    v-for="(file, index) in uploadedVideos"
                    :key="index"
                    class="uploaded-video-card"
                  >
                    <div class="video-preview">
                      <video
                        :src="file.url"
                        class="video-thumbnail"
                        @click="playVideo(file)"
                      ></video>
                      <div class="play-overlay" @click="playVideo(file)">
                        <div class="play-icon">▶</div>
                      </div>
                      <div
                        class="delete-video-btn"
                        @click="removeUploadedVideo(file)"
                      >
                        <svg
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="delete-icon"
                        >
                          <path
                            d="M18 6L6 18M6 6l12 12"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      </div>
                      <div class="video-duration-overlay">
                        {{ formatDuration(file.duration || 0) }}
                      </div>
                    </div>
                  </div>

                  <!-- 正在上传的视频进度 -->
                  <div
                    v-for="[fileId, progress] in uploadingFiles"
                    :key="`uploading-${fileId}`"
                    class="uploading-video-card"
                  >
                    <div class="upload-progress-container">
                      <div class="upload-progress-bg">
                        <div
                          class="upload-progress-bar"
                          :style="{ width: `${progress}%` }"
                        ></div>
                      </div>
                      <div class="upload-progress-text">{{ progress }}%</div>
                    </div>
                  </div>

                  <!-- 上传按钮 -->
                  <div
                    v-if="
                      uploadedVideos.length < 10 && uploadingFiles.size === 0
                    "
                    class="upload-button-container"
                  >
                    <Upload
                      :file-list="[]"
                      :before-upload="beforeUpload"
                      multiple
                      accept="video/*"
                      :show-upload-list="false"
                      :disabled="ossConfigLoading"
                      class="video-uploader"
                    >
                      <div
                        class="upload-box"
                        :class="{ disabled: ossConfigLoading }"
                      >
                        <div class="upload-icon">+</div>
                        <div class="upload-text">
                          {{ ossConfigLoading ? '配置中...' : '上传视频' }}
                        </div>
                      </div>
                    </Upload>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生成视频按钮 -->
      <div class="generate-video-section">
        <Button
          type="primary"
          size="large"
          :loading="isSubmitting"
          @click="saveClipTask"
          class="generate-video-button"
        >
          {{ isSubmitting ? '正在生成...' : '生成视频' }}
        </Button>
      </div>

      <!-- 右侧视频选择弹窗 -->
      <Drawer
        v-model:open="drawerVisible"
        title="选择视频"
        placement="right"
        :width="600"
        class="video-drawer"
        @close="closeDrawer"
      >
        <!-- 空状态 -->
        <div v-if="videoList.length === 0" class="empty-state">
          <Empty description="暂无视频数据">
            <Button type="primary" @click="goToDigitalHuman">
              点击创作视频
            </Button>
          </Empty>
        </div>

        <!-- 视频列表 -->
        <div v-else class="video-list-container">
          <!-- 视频网格 -->
          <div class="video-grid" v-loading="videoLoading">
            <div
              v-for="video in videoList"
              :key="video.id"
              class="video-item"
              :class="{ selected: tempSelectedVideos.includes(video.id) }"
              @click="toggleVideoSelection(video.id)"
            >
              <div class="video-thumbnail-container">
                <img
                  :src="
                    video.result_cover ||
                    'https://via.placeholder.com/120x214/f0f0f0/cccccc?text=视频'
                  "
                  :alt="video.name"
                  class="thumbnail-image"
                />
                <div class="selection-indicator">
                  <Checkbox
                    :checked="tempSelectedVideos.includes(video.id)"
                    @click.stop="toggleVideoSelection(video.id)"
                  />
                </div>
                <div class="video-name-overlay" :title="video.name">
                  {{ video.name }}
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.total > 0" class="pagination-container">
            <Pagination
              v-model:current="pagination.current"
              :total="pagination.total"
              :page-size="pagination.pageSize"
              :show-size-changer="false"
              :show-quick-jumper="false"
              :show-total="(total) => `共 ${total} 个视频`"
              @change="handlePageChange"
            />
          </div>
        </div>

        <!-- 底部确认按钮 -->
        <template #footer>
          <div class="drawer-footer">
            <Button @click="closeDrawer">取消</Button>
            <Button
              type="primary"
              :disabled="tempSelectedVideos.length === 0"
              @click="confirmSelection"
            >
              确认选择 ({{ tempSelectedVideos.length }})
            </Button>
          </div>
        </template>
      </Drawer>

      <!-- 音量调节弹窗 -->
      <Modal
        v-model:open="volumeModalVisible"
        title="音量调节"
        :width="400"
        @ok="confirmVolumeChange"
        @cancel="cancelVolumeChange"
      >
        <div class="volume-modal-content">
          <div class="volume-info">
            <div class="current-music-name">
              {{
                currentEditingMusic?.id
                  ? getMusicNameById(currentEditingMusic.id)
                  : ''
              }}
            </div>
            <div class="volume-display">当前音量: {{ tempVolume }}</div>
          </div>
          <div class="volume-slider-container">
            <div class="volume-label">音量调节 (1-20)</div>
            <Slider
              v-model:value="tempVolume"
              :min="1"
              :max="20"
              :marks="{ 1: '1', 10: '10', 20: '20' }"
              :tooltip-formatter="(value) => `${value}`"
              class="volume-slider"
            />
          </div>
        </div>
      </Modal>
    </div>
  </Page>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    flex-direction: column;
    gap: 24px;
  }

  .smart-clip-panel {
    width: 100%;
  }

  .switch-row {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .ai-video-mixing-container {
    padding: 16px;
  }

  .control-items-area {
    width: 100%;
  }

  .title-input-item {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .remove-btn {
    align-self: flex-end;
    width: fit-content;
  }

  .switch-row {
    gap: 12px;
  }

  .video-upload-container {
    gap: 8px;
  }

  .template-container {
    gap: 8px;
  }

  .background-container {
    gap: 8px;
  }

  .music-categories {
    gap: 6px;
  }

  .music-category-item {
    padding: 4px 8px;
    font-size: 12px;
  }

  .music-list-container {
    max-height: 300px;
    padding: 8px;
  }

  .music-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .music-item {
    padding: 8px;
  }

  .music-title-text {
    font-size: 13px;
  }

  .music-meta {
    font-size: 11px;
  }

  .play-button {
    width: 32px;
    height: 32px;
  }

  .music-content {
    gap: 8px;
  }

  .generate-video-section {
    padding: 16px;
    margin-top: 24px;
  }

  .generate-video-button {
    min-width: 160px;
    height: 44px;
    font-size: 15px;
  }
}

.ai-video-mixing-container {
  padding: 24px;
}

/* 左侧整体面板布局 */
.left-panel {
  display: flex;
  gap: 32px;
  width: 100%;
  min-width: 0;
}

/* 左列：视频选择区域 */
.video-selection-column {
  flex-shrink: 0;
}

.video-selection-area {
  display: flex;
  justify-content: flex-start;
}

/* 控制项区域 */
.control-items-area {
  width: 240px;
  margin-top: 24px;
}

.control-item {
  margin-bottom: 20px;
}

.control-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.control-label-block {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 视频标题输入组 */
.title-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title-input-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.title-input {
  flex: 1;
}

.remove-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.add-title-btn {
  width: 100%;
  margin-top: 4px;
}

/* 右列：智能剪辑面板 */
.smart-clip-panel {
  flex: 1;
  min-width: 0;
  padding: 20px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

/* 视频模版样式 */
.template-section {
  width: 100%;
  margin-bottom: 24px;
  overflow: hidden;
}

.template-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.template-container {
  display: flex;
  gap: 12px;
  width: 100%;
  min-width: 0;
  padding-bottom: 16px;
  overflow: auto hidden;
  scrollbar-color: hsl(var(--primary)) #f1f1f1;
  scrollbar-width: thin;
  -webkit-overflow-scrolling: touch;
}

.template-container::-webkit-scrollbar {
  height: 8px;
}

.template-container::-webkit-scrollbar-track {
  margin: 0 4px;
  background: #f1f1f1;
  border-radius: 4px;
}

.template-container::-webkit-scrollbar-thumb {
  min-width: 20px;
  background: hsl(var(--primary));
  border-radius: 4px;
}

.template-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 80%);
}

.template-container::-webkit-scrollbar-corner {
  background: transparent;
}

.template-item {
  position: relative;
  flex: 0 0 120px;
  width: 120px;
  height: 214px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.template-item.selected {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px hsl(var(--primary) / 30%);
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-selected-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: hsl(var(--primary));
  border-radius: 50%;
}

.check-icon {
  width: 12px;
  height: 12px;
  color: white;
}

/* 视频背景样式 */
.background-section {
  width: 100%;
  margin-bottom: 24px;
  overflow: hidden;
}

.background-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.background-container {
  display: flex;
  gap: 12px;
  width: 100%;
  min-width: 0;
  padding-bottom: 16px;
  overflow: auto hidden;
  scrollbar-color: hsl(var(--primary)) #f1f1f1;
  scrollbar-width: thin;
  -webkit-overflow-scrolling: touch;
}

.background-container::-webkit-scrollbar {
  height: 8px;
}

.background-container::-webkit-scrollbar-track {
  margin: 0 4px;
  background: #f1f1f1;
  border-radius: 4px;
}

.background-container::-webkit-scrollbar-thumb {
  min-width: 20px;
  background: hsl(var(--primary));
  border-radius: 4px;
}

.background-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 80%);
}

.background-container::-webkit-scrollbar-corner {
  background: transparent;
}

.background-item {
  position: relative;
  flex: 0 0 120px;
  width: 120px;
  height: 214px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.background-item:hover {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.background-item.selected {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px hsl(var(--primary) / 30%);
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.background-selected-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: hsl(var(--primary));
  border-radius: 50%;
}

/* 背景音乐样式 */
.music-section {
  width: 100%;
  margin-bottom: 24px;
  overflow: hidden;
}

.music-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 音乐分类样式 */
.music-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.music-category-item {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  background: #f0f0f0;
  border: 1px solid transparent;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.music-category-item:hover {
  color: hsl(var(--primary));
  background: hsl(var(--primary) / 10%);
}

.music-category-item.selected {
  color: white;
  background: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

/* 音乐列表样式 */
.music-list-container {
  max-height: 400px;
  padding: 12px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.music-list-container::-webkit-scrollbar {
  width: 6px;
}

.music-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.music-list-container::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 3px;
}

.music-list-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 80%);
}

.music-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.music-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 12px;
  cursor: pointer;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.music-item:hover {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.music-item.selected {
  background: hsl(var(--primary) / 5%);
  border-color: hsl(var(--primary));
}

.music-content {
  display: flex;
  flex: 1;
  gap: 12px;
  align-items: flex-start;
}

.music-info {
  flex: 1;
  min-width: 0;
}

.music-title-text {
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  color: #333;
  white-space: nowrap;
}

.music-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
}

.music-author {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
  white-space: nowrap;
}

.music-duration {
  flex-shrink: 0;
  color: #999;
}

.play-button {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.music-selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: hsl(var(--primary));
  border-radius: 50%;
}

/* 已选择音频样式 */
.selected-music-section {
  padding: 12px;
  margin-top: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.selected-music-title {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.selected-music-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-music-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.selected-music-info {
  flex: 1;
  min-width: 0;
}

.selected-music-name {
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.selected-music-volume {
  font-size: 12px;
  color: #666;
}

.selected-music-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.volume-adjust-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.volume-adjust-button:hover {
  transform: scale(1.1);
}

.remove-music-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  background: #ff4d4f;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.remove-music-button:hover {
  background: #ff7875;
}

.remove-icon {
  width: 10px;
  height: 10px;
  color: white;
}

/* 音量调节弹窗样式 */
.volume-modal-content {
  padding: 16px 0;
}

.volume-info {
  margin-bottom: 24px;
  text-align: center;
}

.current-music-name {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.volume-display {
  font-size: 14px;
  color: #666;
}

.volume-slider-container {
  padding: 0 16px;
}

.volume-label {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.volume-slider {
  margin-bottom: 16px;
}

.panel-item {
  margin-bottom: 20px;
}

/* 开关行布局 */
.switch-row {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  align-items: center;
}

.switch-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.panel-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.batch-multiple-input {
  width: 80px;
}

/* 上传区域样式 */
.upload-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.upload-description {
  margin-bottom: 16px;
  font-size: 13px;
  line-height: 1.5;
  color: #666;
}

.highlight-text {
  font-weight: 500;
  color: #ff4d4f;
}

/* 视频上传容器 */
.upload-area {
  width: 100%;
}

.video-upload-container {
  display: flex;
  gap: 12px;
  padding-bottom: 8px;
  overflow-x: auto;
}

.video-upload-container::-webkit-scrollbar {
  height: 6px;
}

.video-upload-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.video-upload-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.video-upload-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 已上传视频卡片 */
.uploaded-video-card {
  flex-shrink: 0;
  width: 120px;
  overflow: hidden;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.uploaded-video-card:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.video-preview {
  position: relative;
  width: 120px;
  height: 214px;
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  pointer-events: none;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 30%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-preview:hover .play-overlay {
  opacity: 1;
}

.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 16px;
  color: #333;
  background: rgb(255 255 255 / 90%);
  border-radius: 50%;
}

.delete-video-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  background: rgb(0 0 0 / 60%);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.delete-video-btn:hover {
  background: rgb(255 0 0 / 80%);
}

.delete-video-btn .delete-icon {
  width: 10px;
  height: 10px;
  color: white;
}

.video-duration-overlay {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 8px 6px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-align: center;
  background: rgb(0 0 0 / 70%);
  backdrop-filter: blur(2px);
}

/* 上传按钮容器 */
.upload-button-container {
  flex-shrink: 0;
}

.video-uploader {
  width: 120px;
  height: 214px;
}

.upload-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 214px;
  cursor: pointer;
  background: white;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-box:hover {
  background: #f0f8ff;
  border-color: #1890ff;
}

.upload-box.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-icon {
  margin-bottom: 8px;
  font-size: 24px;
  color: #bfbfbf;
}

.upload-text {
  font-size: 12px;
  color: #666;
}

/* 上传进度样式 */
.uploading-video-card {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 214px;
  padding: 16px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.upload-progress-container {
  width: 100%;
  text-align: center;
}

.upload-progress-bg {
  width: 100%;
  height: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  background-color: #e9ecef;
  border-radius: 4px;
}

.upload-progress-bar {
  height: 100%;
  background-color: #1890ff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.upload-progress-text {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.video-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 427px;
  cursor: pointer;
  background-color: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.video-selector:hover {
  background-color: #f0f8ff;
  border-color: #1890ff;
}

.selector-content {
  color: #666;
  text-align: center;
}

.selector-icon {
  margin-bottom: 16px;
}

.selector-icon .icon {
  width: 48px;
  height: 48px;
  color: #bfbfbf;
}

.selector-text {
  margin-bottom: 8px;
  font-size: 16px;
}

.selected-count {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

/* Drawer样式 */
.video-drawer :deep(.ant-drawer-body) {
  padding: 16px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.video-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-grid {
  display: grid;
  flex: 1;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  max-height: calc(100vh - 300px);
  padding-bottom: 16px;
  overflow-y: auto;
}

.video-item {
  width: 120px;
  overflow: hidden;
  cursor: pointer;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.video-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.video-item.selected {
  background-color: #f0f8ff;
  border-color: #1890ff;
}

.video-thumbnail-container {
  position: relative;
  width: 120px;
  height: 214px;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
}

.video-name-overlay {
  position: absolute;
  right: 0;
  bottom: 25px;
  left: 0;
  padding: 8px 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
  background: rgb(0 0 0 / 70%);
  backdrop-filter: blur(2px);
}

.pagination-container {
  padding-top: 16px;
  margin-top: 16px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.drawer-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 选中视频展示样式 */
.selected-videos-display {
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 427px;
}

.video-carousel-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
}

.video-carousel {
  height: 100%;
}

.video-carousel :deep(.ant-carousel .slick-slide) {
  height: 387px;
}

.video-carousel :deep(.ant-carousel .slick-list) {
  height: 100%;
}

.video-carousel :deep(.ant-carousel .slick-track) {
  height: 100%;
}

/* 轮播图箭头样式 */
.video-carousel :deep(.ant-carousel .slick-prev),
.video-carousel :deep(.ant-carousel .slick-next) {
  z-index: 10;
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgb(0 0 0 / 60%);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.video-carousel :deep(.ant-carousel .slick-prev:hover),
.video-carousel :deep(.ant-carousel .slick-next:hover) {
  background: rgb(0 0 0 / 80%);
}

.video-carousel :deep(.ant-carousel .slick-prev) {
  left: 10px;
}

.video-carousel :deep(.ant-carousel .slick-next) {
  right: 10px;
}

.video-carousel :deep(.ant-carousel .slick-prev::before),
.video-carousel :deep(.ant-carousel .slick-next::before) {
  font-size: 16px;
  color: white;
}

/* 轮播图指示点样式 */
.video-carousel :deep(.ant-carousel .slick-dots) {
  bottom: 10px;
}

.video-carousel :deep(.ant-carousel .slick-dots li button) {
  background: rgb(255 255 255 / 60%);
  border-radius: 50%;
}

.video-carousel :deep(.ant-carousel .slick-dots li.slick-active button) {
  background: white;
}

.carousel-slide {
  height: 387px;
}

.selected-video-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.selected-video-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.delete-button {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  background: rgb(0 0 0 / 60%);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.delete-button:hover {
  background: rgb(255 0 0 / 80%);
}

.delete-icon {
  width: 12px;
  height: 12px;
  color: white;
}

.reselect-button-container {
  margin-top: 12px;
  text-align: center;
}

.reselect-button {
  width: 100%;
}

/* 生成视频按钮样式 */
.generate-video-section {
  padding: 24px;
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

.generate-video-button {
  min-width: 200px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}
</style>
